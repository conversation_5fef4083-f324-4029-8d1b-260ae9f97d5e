# These are individual fields that pertain to a custom form. These fields have associated
#   values, which are what gets displayed in the views.

class CustomFormField < ActiveRecord::Base
  attr_accessor :current_company_user, :skip_event_logs
  has_many :custom_form_values, dependent: :destroy
  has_one :field_position, dependent: :destroy
  has_many :custom_form_field_permissions, dependent: :destroy
  has_many :expanded_form_field_permissions, dependent: :destroy

  belongs_to :msp_templates_custom_form_field, class_name: "Msp::Templates::CustomFormField", optional: true
  belongs_to :custom_form

  validates_presence_of :label, :field_attribute_type
  validates :custom_form_field_permissions, presence: true
  validate :private_fields, on: [:create, :update], if: :helpdesk_form?
  validates :label,
            length: { minimum: 2 }
  # uniqueness: { scope: :custom_form_id },
  validate :uniqueness_of_field_name

  delegate :company, to: :custom_form
  before_create :set_default_values
  before_validation :create_default_permissions
  enum field_attribute_type: CustomFormFieldTemplate.field_attribute_types

  accepts_nested_attributes_for :field_position
  accepts_nested_attributes_for :custom_form_field_permissions
  after_commit :create_activity, unless: :skip_event_logs
  after_update :update_list_views
  after_update :update_sla_details
  after_destroy :update_list_views

  def uniqueness_of_field_name
    duplicates = custom_form.custom_form_fields.select { |cff| cff.name == name }
    if duplicates.count > 1
      errors.add(:name, 'must be unique per custom form')
    end
  end

  def options_as_json
    self.options.present? ? JSON.parse(self.options) : nil
  rescue JSON::ParserError => e
    return nil
  end

  def create_activity
    if self.current_company_user.present?
      event_log_service = EventLogService.new(self.custom_form, self.current_company_user, check_activity_type, nil, self)
      event_log_service.create_field_activity
    end
  end

  def as_json(options = nil)
    super(options).merge(
      options: options_as_json,
      field_position: self.field_position&.attributes,
      permissions: permissions_as_json
    )
  end

  def specific_permission(company_user, permission_method)
    custom_form_field_permissions.where(permission_method => true).find_each do |permission|
      global = permission.contributor&.group&.include_all
      specific = !global && company_user && permission.contains?(company_user.contributor_id)
      return true if specific || global
    end
    false
  end

  def can_view?(company_user)
    specific_permission(company_user, :can_view)
  end

  def can_edit?(company_user, ticket_id = nil)
    specific_permission(company_user, :can_edit)
  end

  def option_values
    values = JSON.parse(self.options)
    if values.is_a?(Array)
      values
    elsif values.is_a?(Hash)
      values.keys
    else
      nil
    end
  end

  def set_default_values
    if field_attribute_type == 'status'
      self.default_value ||= option_values[0]
    elsif field_attribute_type == 'priority'
      self.default_value ||= option_values.map { |o| o['name'] }.flatten[0]
    elsif field_attribute_type == 'people_list'
      if self.sort_list.blank?
        self.sort_list = "[\"Ascending\",\"First Name, Last Name\"]"
      end
    end
  end

  def permissions_as_json
    permissions_json = []
    self.custom_form_field_permissions.includes(contributor: :group).find_each do |perm|
      # If contributor is nil, then it's for everyone
      perm_list = perm.can_edit ? ['write'] : ['read']
      members_count = perm.contributor.group ? perm.contributor.group.group_members.count : nil
      group_id = perm.contributor.group.present? ? perm.contributor.group.id : nil
      permissions_json << {
        id: perm.id,
        group_id: group_id,
        contributor_id: perm.contributor_id,
        contributor_type: perm.contributor.contributor_type,
        include_all: perm.contributor.group&.include_all,
        member_count: members_count,
        name: perm.contributor.name,
        permissions: perm_list,
      }
    end
    permissions_json
  end

  def helpdesk_form?
    custom_form.company_module == 'helpdesk'
  end

  def private_fields
    errors.add(:custom_form_field, "Required fields can't be private") if required && private
  end

  def create_default_permissions
    if company && custom_form_field_permissions.blank?
      everyone = company.groups.find_by(name: 'Everyone', default: true)
      if everyone
        contrib = everyone.contributor
        self.custom_form_field_permissions << CustomFormFieldPermission.new(contributor: contrib, can_edit: true, can_view: false)
      end
    end
  end

  def singular?
    %w(text text_area static_text rich_text number date phone priority status list category radio_button avatar email department).include?(field_attribute_type)
  end

  def check_activity_type
    if transaction_include_any_action?([:create])
      return 'created'
    elsif  transaction_include_any_action?([:update])
      return 'updated'
    elsif transaction_include_any_action?([:destroy])
      return 'deleted'
    end
  end

  def alpha?
    types = %w{text text_area rich_text number list date phone priority status static_text 
               tag category radio_button checkbox email department}
    types.include?(self.field_attribute_type)
  end

  def numeric?
    !alpha?
  end

  def update_list_views
    if self.saved_change_to_name? || self.saved_change_to_label? || self.saved_change_to_field_attribute_type? || self.destroyed?
      if self.destroyed?
        col_name = self.name
        col_type = self.field_attribute_type
      else
        col_name = self.name_before_last_save
        col_type = self.field_attribute_type_before_last_save
      end
        
      return unless self.custom_form.workspace_id
      cffs = CustomFormField.includes(:custom_form)
        .where(custom_form_fields: {name: col_name, field_attribute_type: col_type}, 
               custom_forms: { workspace_id: self.custom_form.workspace.id })
      if cffs.count == 0
        remove_column_from_tlcs(col_name, col_type)
      elsif cffs.count > 0 && self.saved_change_to_label?
        update_column_description_in_tlcs(col_name, col_type)
      end
    end
  end

  def remove_column_from_tlcs(col_name, col_type)
    tlcs = TicketListColumn.where(workspace_id: self.custom_form.workspace_id)
    tlcs.find_each do |tlc|
      cols_to_remove = tlc.columns.find_all { |col| col['field_name'] == col_name && col['field_type'] == col_type }
      if cols_to_remove.length > 0
        tlc.update_columns(columns: tlc.columns - cols_to_remove)
      end
    end
  end

  def update_column_description_in_tlcs(col_name, col_type)
    tlcs = TicketListColumn.where(workspace_id: custom_form.workspace_id)
    tlcs.find_each do |tlc|
      cols_to_update = tlc.columns.select { |col| col['field_name'] == col_name && col['field_type'] == col_type }
      next if cols_to_update.empty?

      cols_to_update.each { |col| col['description'] = self.label }
      tlc.update_columns(columns: tlc.columns)
    end
  end

  def update_sla_details
    if self.name == "priority" && sla_policies.present?
      sla_policies.each do |sla_policy|
        options = self.option_values.map { |option| option["name"] }
        detail_priorities = sla_policy.details.pluck(:priority)
        remove_priorities = detail_priorities - options
        remove_details = sla_policy.details.where(priority: remove_priorities)
        if remove_details.present?
          remove_details.destroy_all
        end
      end
    end
  end

  def sla_policies
    @sla_policies ||= self.custom_form&.policies
  end
end
