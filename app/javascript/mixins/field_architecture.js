const PAGE_POSITION = {
                        label: "Page Position",
                        type: "position",
                        options: [{ name: "Right", value: "right" }, { name: "Left", value: "left" }],
                      };

const NOTE_FIELD = { label: "Sublabel", subLabel: "A styled note below the input.", name: "note", type: "text", placeholder: "Add an explanatory note if needed." };
const REQUIRED_FIELD = { label: "Is this field required?", name: "required", type: "toggle" };
const REQUIRED_FIELD_FOR_CLOSURE = { label: "Is this field required to close?", name: "requiredToClose", type: "closure_toggle" };

export default {
  methods: {
    getFieldArchitecture(field, followersToggleField, companyModule = null) {
      if (field) {
        switch(field.fieldAttributeType) {
          case "text":
            return {
              fields: [
                NOTE_FIELD,
                { label: "Default Value", name: "defaultValue", type: "text" },
                PAGE_POSITION,
                REQUIRED_FIELD,
                REQUIRED_FIELD_FOR_CLOSURE,
              ],
            };
          case "static_text":
            return {
              fields: [
                { label: "Default Value", name: "defaultValue", type: "text" },
                PAGE_POSITION,
              ],
            };
          case "text_area":
            return {
              fields: [
                NOTE_FIELD,
                { label: "Default Value", name: "defaultValue", type: "text" },
                PAGE_POSITION,
                REQUIRED_FIELD,
                REQUIRED_FIELD_FOR_CLOSURE,
              ],
            };
          case "rich_text":
            return {
              fields: [
                NOTE_FIELD,
                { label: "Default Value", name: "defaultValue", type: "text" },
                PAGE_POSITION,
                REQUIRED_FIELD,
                REQUIRED_FIELD_FOR_CLOSURE,
              ],
            };
          case "number":
            return {
              fields: [
                NOTE_FIELD,
                { label: "Default Value", name: "defaultValue", type: "number" },
                PAGE_POSITION,
                REQUIRED_FIELD,
                REQUIRED_FIELD_FOR_CLOSURE,
              ],
            };
          case "tag":
            return {
              fields: [
                NOTE_FIELD,
                PAGE_POSITION,
                {
                  label: "Add tag",
                  placeholder: "Add new tag",
                  type: "custom_list",
                  nonremovables: [],
                },
                REQUIRED_FIELD,
                REQUIRED_FIELD_FOR_CLOSURE,
              ],
            };
          case "category":
            return {
              fields: [
                NOTE_FIELD,
                PAGE_POSITION,
                REQUIRED_FIELD,
                REQUIRED_FIELD_FOR_CLOSURE,
              ],
            };
          case "department":
            return {
              fields: [
                NOTE_FIELD,
                PAGE_POSITION,
                REQUIRED_FIELD,
                REQUIRED_FIELD_FOR_CLOSURE,
              ],
            };
          case "list":
            return {
              fields: [
                NOTE_FIELD,
                {
                  label: "Add options",
                  placeholder: "Enter option name",
                  type: "custom_list",
                  nonremovables: [],
                },
                PAGE_POSITION,
                {
                  label: "Add prepopulated data",
                  type: "custom_option",
                  options: [{ name: "Country", value: "country" }, { name: "State/Province", value: "state" }],
                },
                {
                  label: "Default Value",
                  name: "defaultValue",
                  type: "list",
                  options: field.options ? field.options.map(op =>  ({ name: op, value: op }) ) : [],
                },
                REQUIRED_FIELD,
                REQUIRED_FIELD_FOR_CLOSURE,
              ],
            };
          case "radio_button":
            return {
              fields: [
                {
                  label: "Add options",
                  placeholder: "Enter option name",
                  type: "custom_list",
                  nonremovables: [],
                },
                {
                  label: "Default Value",
                  name: "defaultValue",
                  type: "list",
                  options: field.options ? field.options.map(op =>  ({ name: op, value: op }) ) : [],
                },
                PAGE_POSITION,
                REQUIRED_FIELD,
                REQUIRED_FIELD_FOR_CLOSURE,
              ],
            };
          case "checkbox":
            return {
              fields: [
                {
                  label: "Add options",
                  placeholder: "Enter option name",
                  type: "custom_list",
                  nonremovables: [],
                },
                PAGE_POSITION,
                {
                  label: "Default Value",
                  name: "defaultValue",
                  type: "multi_list",
                  options: field.options ? field.options.map(op =>  ({ name: op, value: op }) ) : [],
                },
                REQUIRED_FIELD,
                REQUIRED_FIELD_FOR_CLOSURE,
              ],
            };
          case "people_list": {
            const audienceField = {
              label: "Who to include?",
              name: 'audience',
              type: "audience_list",
              options: [{ value: 'teammates', name: "Teammates Only"}, { value: 'guests', name: "Teammates and Guests" }],
            };
            // const shouldShowAudienceField = null;
            const shouldShowAudienceField = !['companyUser', 'location'].includes(companyModule);

            return {
              fields: [
                NOTE_FIELD,
                { label: "Groups", name: "group", type: "group_list" },
                ...(shouldShowAudienceField ? [audienceField] : []),
                {
                  label: "Sort Preferences",
                  type: "sort_list",
                  options: [ "Ascending", "Descending"],
                },
                {
                  label: "Name Format",
                  type: "name_format",
                  options: ["First Name, Last Name", "Last Name, First Name"],
                },
                PAGE_POSITION,
                REQUIRED_FIELD,
                REQUIRED_FIELD_FOR_CLOSURE,
                {
                  label: "Include as Followers in Automated Tasks",
                  name: "isFollowers",
                  type: "followers_toggle",
                },
                ...(followersToggleField
                  ? []
                  : [
                      {
                        label: "Set this field as Followers",
                        name: "isFollowersField",
                        type: "followers_toggle_field",
                      },
                    ]
                ),
                {
                  label: "Default Value",
                  name: "defaultValue",
                  type: "smart_list",
                  fieldAttributeType: field.fieldAttributeType,
                  optionsUrl: "/contributor_options_with_current.json",
                },
              ],
            };
          }
          case "asset_list":
            return {
              fields: [
                NOTE_FIELD,
                {
                  label: "Default Value",
                  name: "defaultValue",
                  type: "smart_list",
                  fieldAttributeType: field.fieldAttributeType,
                  optionsUrl: "/managed_asset_options.json",
                },
                PAGE_POSITION,
                REQUIRED_FIELD,
                REQUIRED_FIELD_FOR_CLOSURE,
              ],
            };
          case "contract_list":
            return {
              fields: [
                NOTE_FIELD,
                {
                  label: "Default Value",
                  name: "defaultValue",
                  type: "smart_list",
                  fieldAttributeType: field.fieldAttributeType,
                  optionsUrl: "/contract_options.json",
                },
                PAGE_POSITION,
                REQUIRED_FIELD,
                REQUIRED_FIELD_FOR_CLOSURE,
              ],
            };
          case "vendor_list":
            return {
              fields: [
                NOTE_FIELD,
                {
                  label: "Default Value",
                  name: "defaultValue",
                  type: "smart_list",
                  fieldAttributeType: field.fieldAttributeType,
                  optionsUrl: "/vendor_options.json",
                },
                PAGE_POSITION,
                REQUIRED_FIELD,
                REQUIRED_FIELD_FOR_CLOSURE,
              ],
            };
          case "telecom_list":
            return {
              fields: [
                NOTE_FIELD,
                {
                  label: "Default Value",
                  name: "defaultValue",
                  type: "smart_list",
                  fieldAttributeType: field.fieldAttributeType,
                  optionsUrl: "/telecom_service_options.json",
                },
                PAGE_POSITION,
                REQUIRED_FIELD,
                REQUIRED_FIELD_FOR_CLOSURE,
              ],
            };
          case "location_list":
            return {
              fields: [
                NOTE_FIELD,
                {
                  label: "Default Value",
                  name: "defaultValue",
                  type: "smart_list",
                  fieldAttributeType: field.fieldAttributeType,
                  optionsUrl: "/location_options.json",
                },
                PAGE_POSITION,
                REQUIRED_FIELD,
                REQUIRED_FIELD_FOR_CLOSURE,
              ],
            };
          case "attachment":
            return {
              fields: [
                NOTE_FIELD,
                REQUIRED_FIELD,
                REQUIRED_FIELD_FOR_CLOSURE,
              ],
            };
          case "avatar":
            return {
              fields: [
                NOTE_FIELD,
                REQUIRED_FIELD,
                REQUIRED_FIELD_FOR_CLOSURE,
              ],
            };
          case "date":
            return {
              fields: [
                NOTE_FIELD,
                { label: "Default Value", name: "defaultValue", type: "date" },
                PAGE_POSITION,
                REQUIRED_FIELD,
                REQUIRED_FIELD_FOR_CLOSURE,
              ],
            };
          case "phone":
            return {
              fields: [
                NOTE_FIELD,
                { label: "Default Value", name: "defaultValue", type: "phone" },
                PAGE_POSITION,
                REQUIRED_FIELD,
                REQUIRED_FIELD_FOR_CLOSURE,
              ],
            };
          case "email":
            return {
              fields: [
                { 
                  label: "Default Value", 
                  name: "defaultValue", 
                  type: "email",
                  placeholder: "Enter email",
                },
                PAGE_POSITION,
                REQUIRED_FIELD,
                REQUIRED_FIELD_FOR_CLOSURE,
              ],
            };
          case "priority":
            return {
              fields: [
                NOTE_FIELD,
                {
                  label: "Add options",
                  placeholder: "Enter option name",
                  type: "custom_priority_list",
                  name: "priority",
                  nonremovables: ["low", "medium", "high"],
                },
                PAGE_POSITION,
                {
                  label: "Default Value",
                  name: "defaultValue",
                  type: "priority",
                  options: field.options,
                },
                REQUIRED_FIELD,
                REQUIRED_FIELD_FOR_CLOSURE,
              ],
            };
          case "status":
            return {
              fields: [
                NOTE_FIELD,
                {
                  label: "Add custom status",
                  placeholder: "Enter custom status name",
                  name: "status",
                  type: "custom_status_list",
                  nonremovables: ["Open", "In Progress", "Closed"],
                },
                PAGE_POSITION,
                {
                  label: "Default Value",
                  name: "defaultValue",
                  type: "status",
                  options: field.options,
                },
                REQUIRED_FIELD,
                REQUIRED_FIELD_FOR_CLOSURE,
              ],
            };
          default:
            return null;
        }

      }
      return null;
    },
  },
};
